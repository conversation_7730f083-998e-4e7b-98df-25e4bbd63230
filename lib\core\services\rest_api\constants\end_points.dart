// ignore_for_file: constant_identifier_names, non_constant_identifier_names

abstract class EndPoints {
  //##########  Base Url  ##########
  static const String baseUrl =
      // 'https://b4f84ebd-016e-4fed-9492-9e3bd664d12b.mock.pstmn.io/';
      'http://94.72.98.154/renva/public/api/';

  //Auth
  static const login = "v1/login";
  static const register = "v1/register";
  static const get_profile = "v1/profile";
  // Content
  static const categories = "categories";
  static const products = "products";
  // static product(int id) => "products/$id";
  static const provider_categories = "v1/provider_categories";
  // static const provider_categories = "v1/top_categories/6";
  static sub_categories(int id) => "v1/sub_categories/$id";

  static const orders = "v1/orders";
  static const all_orders_list = "v1/orders_by_status";
}
