// import 'package:flutter/widgets.dart';

// class AddOrderPage2 extends StatelessWidget {
//   const AddOrderPage2({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/features/add/addorder2/controller.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'package:renvo_app/gen/assets.gen.dart';

class AddOrderPage2 extends StatelessWidget {
  const AddOrderPage2({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddOrderPage2Controller());
    final OrderDataModel order = Get.arguments as OrderDataModel;

    return Scaffold(
      appBar: AppBar(
        // leading: Icon(
        //   Icons.arrow_circle_left_outlined,
        //   color: Colors.white,
        // ),
        toolbarHeight: 40,
        backgroundColor: StyleRepo.deepBlueFegma,
        elevation: 0,
        title: Text("Back", style: TextStyle(color: Colors.white)),
        centerTitle: false,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Background Gradient
          Container(
            height: MediaQuery.of(context).size.height * 0.44,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [StyleRepo.deepBlueFegma, StyleRepo.deebBlue2],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          // Content
          Padding(
              padding: EdgeInsets.all(18),
              child: Assets.images.renvoBackPng.image()),
          // SvgIcon(icon: Assets.icons.renvoBack),
          // Opacity(
          //   opacity: 0.11,
          //   child: Text(
          //     "Renvo",
          //     style: TextStyle(
          //       fontSize: 100,
          //       fontWeight: FontWeight.bold,
          //       letterSpacing: 1.5,
          //       color: Colors.white,
          //     ),
          //   ),
          // ),
          Column(
            children: [
              const SizedBox(height: 28),
              // Logo with light overlay (you can use an SVG or PNG here)

              // Main Icon in the middle (replace with Image.asset if you want)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Assets.icons.logoAddorder.svg(),
              ),
              const SizedBox(height: 10),
              const Text(
                "Add Order",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 25,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 6),
              const Text(
                "Select Services Type to completed order",
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 18),
              // The White Container (main body)
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(34),
                    ),
                  ),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 18, vertical: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 22),
                        const Center(
                          child: Text(
                            "Household Services",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 19,
                            ),
                          ),
                        ),
                        const SizedBox(height: 10),
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text('• ', style: TextStyle(fontSize: 19)),
                                  Expanded(
                                    child: Text(
                                      'Meet your home cleaning needs from A to Z',
                                      style: TextStyle(fontSize: 15),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 3),
                              Row(
                                children: [
                                  Text('• ', style: TextStyle(fontSize: 19)),
                                  Expanded(
                                    child: Text(
                                      'Commitment to the required deadline',
                                      style: TextStyle(fontSize: 15),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 3),
                              Row(
                                children: [
                                  Text('• ', style: TextStyle(fontSize: 19)),
                                  Expanded(
                                    child: Text(
                                      'Ensuring complete safety and confidentiality',
                                      style: TextStyle(fontSize: 15),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 18),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Text(
                            "Choose Subcategory",
                            style: TextStyle(
                              color: Color(0xFF6119B7),
                              fontWeight: FontWeight.bold,
                              fontSize: 15.5,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                        const SizedBox(height: 18),
                        // Subcategories Icons
                        Expanded(
                          child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 5),
                              child: ObsListBuilder(
                                  obs: controller.subCategory,
                                  builder: (context, subCategories) {
                                    if (subCategories.isEmpty) {
                                      return Center(
                                          child: Text("No categories found."));
                                    }
                                    return ListView.separated(
                                        separatorBuilder: (_, __) =>
                                            SizedBox(height: 3),
                                        itemCount: subCategories.length,
                                        itemBuilder: (context, index) {
                                          // final isSelected = controller
                                          //         .selectedSubCategoryId
                                          //         .value ==
                                          //     subCategories[index].id;
                                          return Column(
                                            children: [
                                              Obx(() {
                                                late bool isSelected = controller
                                                        .selectedSubCategoryId
                                                        .value ==
                                                    subCategories[index].id;
                                                return InkWell(
                                                  onTap: () {
                                                    controller
                                                        .selectedSubCategoryId
                                                        .value = subCategories[
                                                            index]
                                                        .id;

                                                    // print(
                                                    //     'Selected SubCategory ID: ${controller.selectedSubCategoryId.value}');
                                                    isSelected = !isSelected;
                                                  },
                                                  child: Container(
                                                    height: 50,
                                                    width: 50,
                                                    decoration: BoxDecoration(
                                                      // color: Colors.grey.shade300,
                                                      color: isSelected
                                                          ? Color(0xFF5B20C7)
                                                              .withOpacity(0.10)
                                                          // : Colors.white,
                                                          : Color(0xFF5B20C7)
                                                              .withOpacity(
                                                                  0.10),
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color: isSelected
                                                            ? Color(0xFF5B20C7)
                                                            : Colors
                                                                .grey.shade300,
                                                        width: 1.3,
                                                      ),
                                                    ),
                                                    child: Icon(
                                                      Icons.cleaning_services,
                                                      color: isSelected
                                                          // ? Colors.white
                                                          // : Color(0xFF5B20C7),
                                                          ? Color(0xFF5B20C7)
                                                          : Colors.white,
                                                      size: 36,
                                                    ),
                                                  ),
                                                );
                                              }),
                                              Text(subCategories[index]
                                                  .id
                                                  .toString()),
                                              Text(
                                                  '${subCategories[index].title}'),
                                            ],
                                          );
                                        });
                                  })),
                        ),
                        const Spacer(),
                        // Continue Button
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 18.0),
                          child: Center(
                            child: SizedBox(
                              width: double.infinity,
                              child:
                              ElevatedButton(
                                onPressed: () {
                                  // Get.toNamed(Pages.add_order3.value,
                                  //     arguments: {
                                  //       "subCategoryId": controller
                                  //           .selectedSubCategoryId.value,
                                  //       "categoryId": controller.id_cat,
                                  //     });
                                  order.subCategoryId =
                                      controller.selectedSubCategoryId.value;
                                  Get.toNamed(Pages.add_order3.value,
                                      arguments: order);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: StyleRepo.deebBlue2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(40),
                                  ),
                                  padding: const EdgeInsets.all(16),
                                ),
                                child: const Text(
                                  "Continue",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 17,
                                      color: Colors.white),
                                ),
                              ),
                               ElevatedButton(
                                onPressed: () {
                                  // Get.toNamed(Pages.add_order3.value,
                                  //     arguments: {
                                  //       "subCategoryId": controller
                                  //           .selectedSubCategoryId.value,
                                  //       "categoryId": controller.id_cat,
                                  //     });
                                  order.subCategoryId =
                                      controller.selectedSubCategoryId.value;
                                  Get.toNamed(Pages.add_order3.value,
                                      arguments: order);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: StyleRepo.deebBlue2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(40),
                                  ),
                                  padding: const EdgeInsets.all(16),
                                ),
                                child: const Text(
                                  "Continue",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 17,
                                      color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 16,
                        )
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}







// ElevatedButton(
//                                 onPressed: () {
//                                   // Get.toNamed(Pages.add_order3.value,
//                                   //     arguments: {
//                                   //       "subCategoryId": controller
//                                   //           .selectedSubCategoryId.value,
//                                   //       "categoryId": controller.id_cat,
//                                   //     });
//                                   order.subCategoryId =
//                                       controller.selectedSubCategoryId.value;
//                                   Get.toNamed(Pages.add_order3.value,
//                                       arguments: order);
//                                 },
//                                 style: ElevatedButton.styleFrom(
//                                   backgroundColor: StyleRepo.deebBlue2,
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(40),
//                                   ),
//                                   padding: const EdgeInsets.all(16),
//                                 ),
//                                 child: const Text(
//                                   "Continue",
//                                   style: TextStyle(
//                                       fontWeight: FontWeight.bold,
//                                       fontSize: 17,
//                                       color: Colors.white),
//                                 ),
//                               ),