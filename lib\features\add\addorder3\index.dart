// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:renvo_app/core/routes/routes.dart';
// import 'package:renvo_app/features/add/addorder3/controller.dart';
// import 'package:renvo_app/gen/assets.gen.dart';

// class AddOrderPage3 extends StatelessWidget {
//   final ServiceTypeController controller = Get.put(ServiceTypeController());

//   AddOrderPage3({super.key});

//   @override
//   Widget build(BuildContext context) {
//     const colorMain = Color(0xFF5B20C7);

//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.spaceAround,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Assets.images.logoBlueImg.image(),
//             SizedBox(height: 50),
//             Text(
//               "Select Service Type",
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 fontSize: 19,
//               ),
//               textAlign: TextAlign.center,
//             ),
//             SizedBox(height: 12),
//             Obx(() => Column(
//                   children: [
//                     _radioTile(
//                       title: "As Soon As Possible",
//                       value: 0,
//                       groupValue: controller.selectedType.value,
//                       onChanged: (val) => controller.selectedType.value = val!,
//                       colorMain: colorMain,
//                     ),
//                     SizedBox(height: 16),
//                     _radioTile(
//                       title: "Specific Date",
//                       value: 1,
//                       groupValue: controller.selectedType.value,
//                       onChanged: (val) => controller.selectedType.value = val!,
//                       colorMain: colorMain,
//                     ),
//                     if (controller.selectedType.value == 1) ...[
//                       SizedBox(height: 16),
//                       // تاريخ
//                       InkWell(
//                         onTap: () async {
//                           final picked = await showDatePicker(
//                             context: context,
//                             initialDate:
//                                 controller.selectedDate.value ?? DateTime.now(),
//                             firstDate: DateTime.now(),
//                             lastDate: DateTime(2100),
//                           );
//                           if (picked != null)
//                             controller.selectedDate.value = picked;
//                         },
//                         child: Container(
//                           padding: EdgeInsets.symmetric(
//                               vertical: 14, horizontal: 14),
//                           decoration: BoxDecoration(
//                             border:
//                                 Border.all(color: colorMain.withOpacity(.25)),
//                             borderRadius: BorderRadius.circular(10),
//                             color: Colors.white,
//                           ),
//                           child: Row(
//                             children: [
//                               Icon(Icons.date_range, color: colorMain),
//                               SizedBox(width: 10),
//                               Obx(() => Text(
//                                     controller.selectedDate.value != null
//                                         ? "${controller.selectedDate.value!.toLocal()}"
//                                             .split(' ')[0]
//                                         : "Select date",
//                                     style: TextStyle(
//                                         fontSize: 15, color: colorMain),
//                                   )),
//                               // Obx(() => Text(
//                               //       controller.selectedTime.value != null
//                               //           ? _format24(
//                               //               controller.selectedTime.value!)
//                               //           : "Select time",
//                               //       style: TextStyle(
//                               //           fontSize: 15, color: colorMain),
//                               //     )),
//                             ],
//                           ),
//                         ),
//                       ),
//                       SizedBox(height: 10),
//                       // الوقت
//                       InkWell(
//                         onTap: () async {
//                           final picked = await showTimePicker(
//                             context: context,
//                             initialTime: controller.selectedTime.value ??
//                                 TimeOfDay.now(),
//                           );
//                           if (picked != null)
//                             controller.selectedTime.value = picked;
//                         },
//                         child: Container(
//                           padding: EdgeInsets.symmetric(
//                               vertical: 14, horizontal: 14),
//                           decoration: BoxDecoration(
//                             border:
//                                 Border.all(color: colorMain.withOpacity(.25)),
//                             borderRadius: BorderRadius.circular(10),
//                             color: Colors.white,
//                           ),
//                           child: Row(
//                             children: [
//                               Icon(Icons.access_time, color: colorMain),
//                               SizedBox(width: 10),
//                               // Obx(() => Text(
//                               //       controller.selectedTime.value != null
//                               //           ? controller.selectedTime.value!
//                               //               .format(context)
//                               //           : "Select time",
//                               //       style: TextStyle(
//                               //           fontSize: 15, color: colorMain),
//                               //     )),
//                               Obx(() => Text(
//                                     controller.selectedTime.value != null
//                                         ? _format24(
//                                             controller.selectedTime.value!)
//                                         : "Select time",
//                                     style: TextStyle(
//                                         fontSize: 15, color: colorMain),
//                                   )),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ]
//                   ],
//                 )),
//             // Obx(() => Column(
//             //       children: [
//             //         _radioTile(
//             //           title: "As Soon As Possible",
//             //           value: 0,
//             //           groupValue: controller.selectedType.value,
//             //           onChanged: (val) => controller.selectedType.value = val!,
//             //           colorMain: colorMain,
//             //         ),
//             //         SizedBox(height: 16),
//             //         _radioTile(
//             //           title: "Specific Date",
//             //           value: 1,
//             //           groupValue: controller.selectedType.value,
//             //           onChanged: (val) => controller.selectedType.value = val!,
//             //           colorMain: colorMain,
//             //         ),
//             //       ],
//             //     )),
//             // Spacer(),
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 30),
//               child: SizedBox(
//                 width: double.infinity,
//                 height: 48,
//                 child: ElevatedButton(
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: colorMain,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(22),
//                     ),
//                     elevation: 0,
//                   ),
//                   onPressed: () {
//                     Get.toNamed(Pages.add_order4.value);
//                     // استخدم controller.selectedType.value عند المتابعة
//                   },
//                   child: const Text(
//                     "Continue",
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontSize: 16.5,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(height: 35),
//             IconButton(
//                 onPressed: () {
//                   Get.toNamed(Pages.add_order4.value);
//                 },
//                 icon: Icon(Icons.abc_outlined))
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _radioTile({
//     required String title,
//     required int value,
//     required int groupValue,
//     required ValueChanged<int?> onChanged,
//     required Color colorMain,
//   }) {
//     return AnimatedContainer(
//       duration: const Duration(milliseconds: 170),
//       curve: Curves.easeInOut,
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//       decoration: BoxDecoration(
//         color:
//             value == groupValue ? const Color(0xFFF3F0FD) : Colors.transparent,
//         border: Border.all(
//           color: value == groupValue ? colorMain : Colors.grey.shade300,
//           width: 1.3,
//         ),
//         borderRadius: BorderRadius.circular(22),
//       ),
//       child: Row(
//         children: [
//           Radio<int>(
//             value: value,
//             groupValue: groupValue,
//             activeColor: colorMain,
//             onChanged: onChanged,
//           ),
//           SizedBox(width: 2),
//           Text(
//             title,
//             style: TextStyle(
//               color: value == groupValue ? colorMain : Colors.grey[800],
//               fontWeight:
//                   value == groupValue ? FontWeight.bold : FontWeight.normal,
//               fontSize: 15.5,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// String _format24(TimeOfDay t) {
//   final hours = t.hour.toString().padLeft(2, '0');
//   final minutes = t.minute.toString().padLeft(2, '0');
//   return "$hours:$minutes:00";
// }

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/add/addorder3/controller.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'package:renvo_app/gen/assets.gen.dart';
import 'package:renvo_app/core/routes/routes.dart';

class AddOrderPage3 extends StatelessWidget {
  final ServiceTypeController controller = Get.put(ServiceTypeController());
  final OrderDataModel order = Get.arguments as OrderDataModel;

  AddOrderPage3({super.key});

  @override
  Widget build(BuildContext context) {
    const colorMain = Color(0xFF5B20C7);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Assets.images.logoBlueImg.image(),
            SizedBox(height: 50),
            Text(
              "Select Service Type",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 19),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            Obx(() => Column(
                  children: [
                    _radioTile(
                      title: "As Soon As Possible",
                      value: 0,
                      groupValue: controller.selectedType.value,
                      onChanged: (val) => controller.selectedType.value = val!,
                      colorMain: colorMain,
                    ),
                    SizedBox(height: 16),
                    _radioTile(
                      title: "Specific Date",
                      value: 1,
                      groupValue: controller.selectedType.value,
                      onChanged: (val) => controller.selectedType.value = val!,
                      colorMain: colorMain,
                    ),
                    if (controller.selectedType.value == 1) ...[
                      SizedBox(height: 16),
                      // اختيار التاريخ
                      InkWell(
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate:
                                controller.selectedDate.value ?? DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate: DateTime(2100),
                          );
                          if (picked != null)
                            controller.selectedDate.value = picked;
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 14, horizontal: 14),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: colorMain.withOpacity(.25)),
                            borderRadius: BorderRadius.circular(10),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.date_range, color: colorMain),
                              SizedBox(width: 10),
                              Obx(() => Text(
                                    controller.formattedDate ?? "Select date",
                                    style: TextStyle(
                                        fontSize: 15, color: colorMain),
                                  )),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      // اختيار الوقت
                      InkWell(
                        onTap: () async {
                          final picked = await showTimePicker(
                            context: context,
                            initialTime: controller.selectedTime.value ??
                                TimeOfDay.now(),
                          );
                          if (picked != null)
                            controller.selectedTime.value = picked;
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 14, horizontal: 14),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: colorMain.withOpacity(.25)),
                            borderRadius: BorderRadius.circular(10),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, color: colorMain),
                              SizedBox(width: 10),
                              Obx(() => Text(
                                    controller.formattedTime ?? "Select time",
                                    style: TextStyle(
                                        fontSize: 15, color: colorMain),
                                  )),
                            ],
                          ),
                        ),
                      ),
                    ]
                  ],
                )),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorMain,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22)),
                    elevation: 0,
                  ),
                  onPressed: () {
                    order.serviceType = controller.selectedType.value;
                    if (controller.selectedType.value == 1) {
                      order.selectedDate = controller.selectedDate.value;
                      order.selectedTime = controller.formattedTime;
                    } else {
                      order.selectedDate = null;
                      order.selectedTime = null;
                    }
                    Get.toNamed(Pages.add_order4.value, arguments: order);
                  },
                  //  {
                  // هنا ممكن تتحقق من المدخلات قبل الانتقال إذا أحببت
                  // Get.toNamed(Pages.add_order4.value);
                  // },
                  child: const Text(
                    "Continue",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.5,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 35),
            IconButton(
                onPressed: () {
                  Get.toNamed(Pages.add_order4.value);
                },
                icon: Icon(Icons.abc_outlined))
          ],
        ),
      ),
    );
  }

  Widget _radioTile({
    required String title,
    required int value,
    required int groupValue,
    required ValueChanged<int?> onChanged,
    required Color colorMain,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 170),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color:
            value == groupValue ? const Color(0xFFF3F0FD) : Colors.transparent,
        border: Border.all(
          color: value == groupValue ? colorMain : Colors.grey.shade300,
          width: 1.3,
        ),
        borderRadius: BorderRadius.circular(22),
      ),
      child: Row(
        children: [
          Radio<int>(
            value: value,
            groupValue: groupValue,
            activeColor: colorMain,
            onChanged: onChanged,
          ),
          SizedBox(width: 2),
          Text(
            title,
            style: TextStyle(
              color: value == groupValue ? colorMain : Colors.grey[800],
              fontWeight:
                  value == groupValue ? FontWeight.bold : FontWeight.normal,
              fontSize: 15.5,
            ),
          ),
        ],
      ),
    );
  }
}
