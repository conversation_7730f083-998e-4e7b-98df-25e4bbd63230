import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/features/add/addorderfinal/controller.dart';
import 'package:renvo_app/features/add/models/order_models.dart';

class OrderSummaryPage extends StatelessWidget {
  const OrderSummaryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OrderSummaryController());
    final OrderDataModel order = Get.arguments as OrderDataModel;
    const colorMain = Color(0xFF5B20C7);

    // dummy category name, replace with your logic
    String categoryName =
        'Cleaning'; // إذا عندك اسم الفئة من الصفحة الأولى ضعها هنا

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text("View Order", style: TextStyle(color: Colors.black)),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: [
            // Category
            const SizedBox(height: 8),
            const Text(
              "Category",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
            ),
            Text(
              categoryName,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),

            // Pictures
            const Text(
              "Picture",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 70,
              child: order.images != null && order.images!.isNotEmpty
                  ? ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: order.images!.length,
                      separatorBuilder: (_, __) => const SizedBox(width: 10),
                      itemBuilder: (_, i) => ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.file(
                          File(order.images![i]),
                          width: 70,
                          height: 70,
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : const Text("No pictures added"),
            ),
            const SizedBox(height: 18),

            // Service Type
            const Text(
              "Services Type",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
            ),
            const SizedBox(height: 6),
            Text(
              order.serviceType == 0 ? "As soon as possible" : "Specific date",
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),

            // Date & Time (if exists)
            if (order.serviceType == 1)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Date & Time",
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      const Icon(Icons.date_range,
                          size: 18, color: Colors.grey),
                      const SizedBox(width: 6),
                      Text(
                        order.selectedDate != null
                            ? "${order.selectedDate!.year}-${order.selectedDate!.month.toString().padLeft(2, '0')}-${order.selectedDate!.day.toString().padLeft(2, '0')}"
                            : "-",
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(width: 14),
                      const Icon(Icons.access_time,
                          size: 18, color: Colors.grey),
                      const SizedBox(width: 6),
                      Text(
                        order.selectedTime ?? "-",
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),

            // Description
            const Text(
              "Description",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
            ),
            const SizedBox(height: 6),
            Text(
              order.description ?? "-",
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 18),

            // Price Range
            const Text(
              "Price Range",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
            ),
            const SizedBox(height: 6),
            Text(
              order.minPrice != null && order.maxPrice != null
                  ? "${order.minPrice!.toInt()} - ${order.maxPrice!.toInt()} SEK"
                  : "-",
              style: TextStyle(
                  fontWeight: FontWeight.bold, color: colorMain, fontSize: 15),
            ),
            const SizedBox(height: 36),

            // Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Get.back(); // أو أي منطق إلغاء
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.purple.shade100),
                      backgroundColor: Colors.purple.shade50,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(13),
                      ),
                    ),
                    child: const Text("Cancel",
                        style: TextStyle(color: Colors.purple)),
                  ),
                ),
                const SizedBox(width: 18),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      // هنا ترسل البيانات للباك اند
                      print(order.toJson());

                      // استبدلها بالمعرف الصحيح للمستخدم/المزود

                      await controller.sendOrderToBackend(order);

                      // بعد النجاح يمكنك الانتقال لصفحة نجاح

                      // مثال: await ApiService.sendOrder(order.toJson());
                      // ثم اذهب لصفحة success أو main...
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorMain,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(13),
                      ),
                    ),
                    child: const Text("Confirm",
                        style: TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
