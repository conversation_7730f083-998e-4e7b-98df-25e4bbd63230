import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/add_offer/add%20_offer1/controller.dart';
// import controller file

class AddOfferPage1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final AddOffer1Controller controller = Get.put(AddOffer1Controller());

    return Scaffold(
      appBar: AppBar(
        title: Text(" Add Offer"),
        backgroundColor: Color(0xFF5B20C7),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Price",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
              // const SizedBox(height: 8),
              TextField(
                controller: controller.priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: "Enter the price  ",
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
                onChanged: (value) => controller.price.value = value,
              ),
              // const SizedBox(height: 8),
              Text(" Duration",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
              const SizedBox(height: 4),
              Row(
                children: [
                  // الحقل الرقمي
                  Flexible(
                    flex: 2,
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        hintText: "Enter the duration ",
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      onChanged: (value) => controller.duration.value = value,
                    ),
                  ),
                  const SizedBox(width: 10),
                  // الـ Dropdown للوحدة (Obx)
                  Flexible(
                    flex: 2,
                    child: Obx(() => DropdownButtonFormField<String>(
                          value: controller.unit.value,
                          items: controller.units
                              .map((u) => DropdownMenuItem(
                                    value: u,
                                    child:
                                        Text(u, style: TextStyle(fontSize: 15)),
                                  ))
                              .toList(),
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12)),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 6),
                          ),
                          onChanged: (v) {
                            if (v != null) controller.unit.value = v;
                          },
                        )),
                  ),
                ],
              ),
              // Align(
              //   alignment: Alignment.centerLeft,
              //   child: Text(
              //     "Description",
              //     style: TextStyle(fontWeight: FontWeight.w600),
              //   ),
              // ),
              // const SizedBox(height: 8),
              // Container(
              //   decoration: BoxDecoration(
              //     color: Colors.white,
              //     borderRadius: BorderRadius.circular(14),
              //     border: Border.all(color: Colors.grey.shade300),
              //   ),
              //   child: TextField(
              //     // controller: controller.descController,
              //     minLines: 4,
              //     maxLines: 8,
              //     decoration: const InputDecoration(
              //       hintText: "Add Description",
              //       border: InputBorder.none,
              //       contentPadding:
              //           EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              //     ),
              //   ),
              // ),
              // Spacer(),
              // SizedBox(
              //   height: 250,
              //   // width: 200,
              // ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Description",
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: TextField(
                  controller: controller.descController,
                  minLines: 4,
                  maxLines: 8,
                  decoration: const InputDecoration(
                    hintText: "Add Description",
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Upload Photos",
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => GestureDetector(
                    onTap: controller.pickImages,
                    child: Container(
                      width: double.infinity,
                      height: 100,
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: Colors.grey.shade300, width: 1.1),
                        borderRadius: BorderRadius.circular(14),
                        color: Colors.grey[100],
                      ),
                      child: controller.images.isEmpty
                          ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(Icons.upload,
                                    color: Color(0xFF5B20C7), size: 30),
                                SizedBox(height: 6),
                                Text("Upload Photo",
                                    style: TextStyle(color: Colors.grey)),
                              ],
                            )
                          : ListView(
                              scrollDirection: Axis.horizontal,
                              children: controller.images
                                  .map((img) => Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Image.file(
                                            File(img.path),
                                            height: 80,
                                            width: 80,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ))
                                  .toList(),
                            ),
                    ),
                  )),
              SizedBox(
                height: 20,
              ),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: controller.sendOffer,
                  child: Text("Continue",
                      style:
                          TextStyle(fontSize: 17, fontWeight: FontWeight.bold)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF5B20C7),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                    padding: EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
