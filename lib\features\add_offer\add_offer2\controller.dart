import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/home/<USER>';

class AddOfferReviewController extends GetxController {
  // المتغيرات التي ستستلمها من arguments
  late String price;
  late String duration;
  late String unit;
  late String description;
  late String id;
  final home_controller = Get.put(HomePageController());
  // late List images; // هنا الصور (تأكد ما إذا كانت XFile أم List<String> مسارات صور)

  // متغير لتحكم حالة التحميل

  @override
  void onInit() {
    // استقبال البيانات القادمة من الصفحة السابقة عبر Get.arguments
    final args = Get.arguments as Map<String, dynamic>;
    price = args['price'] ?? '';
    duration = args['duration'] ?? '';
    unit = args['unit'] ?? '';
    description = args['description'] ?? '';
    id = args['id'].toString();
    // images = args['images'] ?? [];
    final List<XFile> images = (args['images'] as RxList<XFile>).toList();
    super.onInit();
  }

  // دالة الإرسال للباك (POST)
  sendOfferToApi() async {
    FormData formData = FormData();

    // الحقول النصية
    formData.fields
      ..add(MapEntry('order_id', id ?? ''))
      ..add(MapEntry('price', price ?? ''))
      ..add(MapEntry('time', duration ?? ''))
      ..add(MapEntry('time_type', unit ?? ''))
      ..add(MapEntry('description', description ?? ''));
    final response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.new_offer,
          method: RequestMethod.Post,
          body: formData,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );

    if (response.success && response.data != null) {
      final rawList = response.data;
      print("sssssssssssssssssssssssssssss${rawList}");
      home_controller.orders.reset();
      home_controller.fetchOrdersPro;
      Get.until(
        (route) => route.settings.name == Pages.home.value,
      );
      // categories.value = rawList.map((e) => AllCategory.fromJson(e)).toList();
    } else {
      print(response.message ?? "Failed to load categories");
    }
  }
}
