import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/gen/assets.gen.dart';

import 'controller.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RegisterPageController());
    return Scaffold(
        backgroundColor: StyleRepo.deepBlue1,
//       body: Form(
//         key: controller.formKey,
//         child: ListView(
//           children: [
//             SizedBox(height: MediaQuery.sizeOf(context).height * .2),

//             SizedBox(height: 16),
//             TextFormField(
//               validator: (value) {
//                 if (value!.isEmpty) {
//                   return "This field is required";
//                 }
//                 if (!value.contains("@")) {
//                   return "Wrong email";
//                 }
//                 return null;
//               },
//             ),
//             SizedBox(height: 16),
//             TextFormField(
//               obscureText: true,
//             ),
//             SizedBox(height: 16),
//             TextFormField(
//               obscureText: true,
//             ),
//             SizedBox(height: 16),
//             ElevatedButton(
//               onPressed: controller.confirm,
//               child: Text("confirm"),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
        body: Column(
          children: [
            // الجزء العلوي البنفسجي مع اللوجو
            Container(
                color: StyleRepo.deepBlue1,
                height: MediaQuery.sizeOf(context).height * 0.20,
                width: double.infinity,
                // alignment: Alignment.center,
                child: SvgIcon(icon: Assets.icons.logoWhite)),

            //
            Container(
              // جزء الفورم
              height: MediaQuery.sizeOf(context).height * 0.80,
              // width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(36),
                  topRight: Radius.circular(36),
                ),
              ),
              child: Form(
                key: controller.formKey,
                child: Expanded(
                  child: ListView(
                    children: [
                      const Text(
                        "Create New Account",
                        style: TextStyle(
                            fontSize: 22, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const Text("add your information to create new account"),
                      const SizedBox(height: 24),

                      // Phone Number Field
                      TextFormField(
                        keyboardType: TextInputType.phone,
                        decoration: InputDecoration(
                          prefixIcon: Icon(Icons.phone),
                          hintText: "Ex : +999 123 456 789",
                          labelText: "Phone Number",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "Phone number is required";
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Password Field
                      TextFormField(
                        obscureText: true,
                        decoration: InputDecoration(
                          prefixIcon: Icon(Icons.lock),
                          suffixIcon:
                              Icon(Icons.visibility), // مجرد أيقونة ثابتة الآن
                          hintText: "add strong password",
                          labelText: "Password",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Confirm Password Field
                      TextFormField(
                        obscureText: true,
                        decoration: InputDecoration(
                          prefixIcon: Icon(Icons.lock),
                          suffixIcon: Icon(Icons.visibility),
                          hintText: "Confirm Password",
                          labelText: "Confirm Password",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Confirm Button
                      SizedBox(
                        width: MediaQuery.sizeOf(context).width * 0.7,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: controller.confirm,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: StyleRepo.deepBlue1,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: const Text(
                            "Confirm",
                            style: TextStyle(color: StyleRepo.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
