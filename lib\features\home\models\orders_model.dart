class OrderModel {
  final int id;
  final String status;
  final String description;
  final String date;
  final String? startAt;
  final List<String>? galleryUrls;
  final String? customerImage;
  final int customerOrdersCount;
  final String customerName;
  final String? customerEmail;

  OrderModel({
    required this.id,
    required this.status,
    required this.description,
    required this.date,
    required this.startAt,
    required this.galleryUrls,
    required this.customerImage,
    required this.customerOrdersCount,
    required this.customerName,
    this.customerEmail,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    List<String> images = [];
    if (json['gallery'] != null) {
      images = List<Map<String, dynamic>>.from(json['gallery'])
          .map((img) => img['original_url'] as String)
          .toList();
    }

    String? custImg = json['customer']?['avatar']?['original_url'];
    int custOrders = json['customer']?['orders_cnt'] ?? 0;
    String firstName = json['customer']?['first_name'] ?? '';
    String lastName = json['customer']?['last_name'] ?? '';
    String fullName = '$firstName $lastName';

    return OrderModel(
      id: json['id'],
      status: json['status'] ?? '',
      description: json['description'] ?? '',
      date: json['date'] ?? '',
      startAt: json['start_at'],
      galleryUrls: images ?? [],
      customerImage: custImg,
      customerOrdersCount: custOrders,
      customerName: fullName,
      customerEmail: json['customer']?['email'],
    );
  }

}
