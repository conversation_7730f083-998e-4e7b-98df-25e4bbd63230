import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/features/home/<USER>/orders_model.dart';

import 'package:renvo_app/features/home/<USER>/provider_home.dart';

import 'package:renvo_app/core/style/style.dart';
import 'package:renvo_app/core/widgets/image.dart';
// أو مكان موديل الأوردر عندك

class OrderDetailsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final OrderModel order = Get.arguments as OrderModel;
    // print(Get.arguments.runtimeType); // سترى نوع الداتا القادم

    final images = order.galleryUrls;

    return Scaffold(
      backgroundColor: Color(0xFF5B20C7), // اللون العلوي الثابت
      body: Column(
        children: [
          // العنوان وصورة المستخدم وعدد الطلبات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 30),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // صورة المستخدم
                // CircleAvatar(
                //   radius: 25,
                //   backgroundImage: order.customerImage != null
                //       ? NetworkImage(order.customerImage!)
                //       : AssetImage('assets/images/background.png')
                //           as ImageProvider,
                // ),
                const SizedBox(width: 12),
                // الاسم وعدد الأوردرات
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      order.customerName ?? 'User Name',
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    SizedBox(height: 3),
                    Text(
                      order.customerEmail ?? "",
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                  ],
                ),
                Spacer(),
                // عدد الأوردرات (شارة خضراء)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(0),
                      bottomRight: Radius.circular(16),
                      topLeft: Radius.circular(0),
                      bottomLeft: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        "${order?.customerOrdersCount ?? 0}",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
                      Text(
                        "Order",
                        style: TextStyle(color: Colors.white, fontSize: 11),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // تفاصيل الطلب - باقي الصفحة بيضاء
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الصور (إذا موجودة)
                    // if (images!.isNotEmpty) ...[
                    //   Text("Picture",
                    //       style: TextStyle(fontWeight: FontWeight.w600)),
                    //   SizedBox(height: 6),
                    //   Row(
                    //     children: images!
                    //         .map((img) => Padding(
                    //               padding: const EdgeInsets.only(right: 8),
                    //               child: ClipRRect(
                    //                 borderRadius: BorderRadius.circular(10),
                    //                 child: Image.network(
                    //                   img,
                    //                   width: 60,
                    //                   height: 60,
                    //                   fit: BoxFit.cover,
                    //                 ),
                    //               ),
                    //             ))
                    //         .toList(),
                    //   ),
                    //   SizedBox(height: 15),
                    // ],
                    // // الوصف
                    Text("Description",
                        style: TextStyle(fontWeight: FontWeight.w600)),
                    SizedBox(height: 6),
                    Text(
                      order.description ?? "-",
                      style: TextStyle(fontSize: 14, color: Colors.grey[800]),
                    ),
                    SizedBox(height: 18),
                    // التاريخ والوقت
                    Text("Date & Time",
                        style: TextStyle(fontWeight: FontWeight.w600)),
                    SizedBox(height: 5),
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            size: 16, color: Colors.grey),
                        SizedBox(width: 7),
                        Text(order.date ?? "-",
                            style: TextStyle(color: Colors.black87)),
                      ],
                    ),
                    SizedBox(height: 7),
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 16, color: Colors.grey),
                        SizedBox(width: 7),
                        Text(order.startAt ?? "-",
                            style: TextStyle(color: Colors.black87)),
                      ],
                    ),
                    SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        Get.toNamed(Pages.add_offer1.value,
                            arguments: order.id);
                      },
                      child: const Text(
                        "Add Offer ",
                        style: TextStyle(
                            color: Colors.blueAccent,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
