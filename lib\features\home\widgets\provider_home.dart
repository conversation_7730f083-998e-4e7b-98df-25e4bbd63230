// import 'package:flutter/widgets.dart';

// class ProviderHomeWidget extends StatelessWidget {
//   const ProviderHomeWidget({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }

// import 'package:flutter/material.dart';

// class ProviderHomeWidget extends StatelessWidget {
//   // قائمة وهمية لعرض الطلبات (استبدلها لاحقًا بقائمة من الباك)
//   final List<Map<String, String>> orders = [
//     {
//       "name":
//           "Name of the services requesterfdddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd ",
//       "location": "Örbyhus - ICA Supermarket",
//       "date": "12 - Tus - 2024",
//       "time": "03:00 Am",
//       "elapsed": "20 min"
//     },
//     {
//       "name": "Name of the services requester",
//       "location": "Örbyhus - ICA Supermarket",
//       "date": "12 - Tus - 2024",
//       "time": "03:00 Am",
//       "elapsed": "20 min"
//     },
//     {
//       "name": "Name of the services requester",
//       "location": "Örbyhus - ICA Supermarket",
//       "date": "12 - Tus - 2024",
//       "time": "03:00 Am",
//       "elapsed": "20 min"
//     },
//   ];

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // العنوان
//         Padding(
//           padding: const EdgeInsets.only(left: 18.0, top: 18, bottom: 6),
//           child: Text(
//             "Latest Order",
//             style: TextStyle(fontWeight: FontWeight.bold, fontSize: 19),
//           ),
//         ),
//         Padding(
//           padding: const EdgeInsets.only(left: 18.0, bottom: 12),
//           child: Text(
//             "Requests sent from newest to oldest",
//             style: TextStyle(color: Colors.grey, fontSize: 13.5),
//           ),
//         ),

//         // قائمة الطلبات
//         ListView.builder(
//           shrinkWrap: true,
//           physics: NeverScrollableScrollPhysics(),
//           itemCount: orders.length,
//           itemBuilder: (context, index) {
//             final order = orders[index];
//             return Padding(
//               padding:
//                   const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4),
//               child: _OrderCard(order: order),
//             );
//           },
//         ),
//       ],
//     );
//   }
// }

// // ويدجت الكارد نفسها
// class _OrderCard extends StatelessWidget {
//   final Map<String, String> order;
//   const _OrderCard({required this.order});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(18),
//         border: Border.all(color: Colors.grey.shade200),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.09),
//             blurRadius: 5,
//             offset: Offset(0, 2),
//           )
//         ],
//       ),
//       child: Padding(
//         padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // الشريط الجانبي البنفسجي
//             Container(
//               width: 5,
//               height: 65,
//               decoration: BoxDecoration(
//                 color: Color(0xFF5B20C7),
//                 borderRadius: BorderRadius.circular(8),
//               ),
//             ),
//             SizedBox(width: 10),
//             // محتوى الكارد
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // الاسم وزر الإغلاق
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Expanded(
//                         child: Text(
//                           order["name"] ?? "",
//                           style: TextStyle(
//                             fontWeight: FontWeight.bold,
//                             fontSize: 15.5,
//                           ),
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Icon(Icons.cancel, size: 21, color: Colors.red[300]),
//                     ],
//                   ),
//                   SizedBox(height: 4),
//                   Text(
//                     order["location"] ?? "",
//                     style: TextStyle(
//                       color: Colors.grey.shade700,
//                       fontSize: 13.3,
//                     ),
//                   ),
//                   SizedBox(height: 10),
//                   // التاريخ والوقت
//                   Row(
//                     children: [
//                       Icon(Icons.calendar_today,
//                           color: Color(0xFF5B20C7), size: 16),
//                       SizedBox(width: 3),
//                       Text(
//                         order["date"] ?? "",
//                         style: TextStyle(fontSize: 13, color: Colors.grey[800]),
//                       ),
//                       SizedBox(width: 13),
//                       Icon(Icons.access_time,
//                           color: Color(0xFF5B20C7), size: 16),
//                       SizedBox(width: 3),
//                       Text(
//                         order["time"] ?? "",
//                         style: TextStyle(fontSize: 13, color: Colors.grey[800]),
//                       ),
//                       if ((order["elapsed"] ?? "").isNotEmpty) ...[
//                         Spacer(),
//                         Text(
//                           order["elapsed"]!,
//                           style: TextStyle(
//                               fontSize: 12.5, color: Colors.grey[500]),
//                         )
//                       ]
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/home/<USER>';
import 'package:renvo_app/features/home/<USER>/orders_model.dart';

class ProviderHomeWidget extends StatelessWidget {
  // بيانات وهمية للطلبات


  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HomePageController());
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(28),
          topRight: Radius.circular(28),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding:
                const EdgeInsets.only(left: 22, top: 28, right: 12, bottom: 2),
            child: Row(
              children: [
                Text(
                  "Latest Order",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 19),
                ),
                // Spacer(),
                // Icon(Icons.list_alt_rounded, color: Color(0xFF5B20C7)),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 22, bottom: 14, top: 2),
            child: Text(
              "Requests sent from newest to oldest",
              style: TextStyle(color: Colors.grey.shade500, fontSize: 13.3),
            ),
          ),
          Expanded(
            child: ObsListBuilder(
              obs: controller.orders,
              builder: (context, orders) {
                return ListView.builder(
                  padding: EdgeInsets.only(bottom: 18),
                  shrinkWrap: true,
                  // physics: NeverScrollableScrollPhysics(),
                  itemCount: orders.length,
                  itemBuilder: (context, index) {
                    final order = orders[index];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 13),
                      // child: Text("data${order.description}"),
                      // child: buildOrderImages(order),
                      child: InkWell(
                          onTap: () {
                            Get.toNamed(Pages.order_details.value,
                                arguments: order);
                          },
                          child: _OrderCard(order: order)),
                    );
                  },
                );
              },
            ),
          )
          // قائمة الطلبات
          // ListView.separated(
          //   padding: EdgeInsets.only(bottom: 18),
          //   shrinkWrap: true,
          //   physics: NeverScrollableScrollPhysics(),
          //   itemCount: orders.length,
          //   separatorBuilder: (_, __) => SizedBox(height: 13),
          //   itemBuilder: (context, index) {
          //     final order = orders[index];
          //     return _OrderCard(order: order);
          //   },
          // ),
        ],
      ),
    );
  }
}

class _OrderCard extends StatelessWidget {
  final OrderModel order;
  const _OrderCard({required this.order});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(19),
        border: Border.all(color: Colors.grey.shade100),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF5B20C7).withValues(alpha: 0.09),
            blurRadius: 12,
            offset: Offset(0, 4),
          )
        ],
      ),
      child: Row(
        children: [
          // شريط جانبي بنفسجي
          Container(
            width: 6,
            height: 95,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(19),
                bottomLeft: Radius.circular(19),
              ),
              gradient: LinearGradient(
                colors: [
                  StyleRepo.deepBlue1,
                  StyleRepo.deebBlue2,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 14, 14, 14),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order ID
                  Text(
                    "Order #${order.id}",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15.7,
                      color: Color(0xFF323232),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8),
                  // الوصف
                  Row(
                    children: [
                      Icon(Icons.description, color: Colors.grey, size: 17),
                      SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          order.description,
                          style: TextStyle(
                            color: Color(0xFF9093A3),
                            fontSize: 13.5,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  // التاريخ
                  Row(
                    children: [
                      Icon(Icons.calendar_today, color: Colors.grey, size: 16),
                      SizedBox(width: 4),
                      Text(
                        order.date,
                        style: TextStyle(
                          fontSize: 13.3,
                          color: Colors.grey[800],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  // الحالة والوقت
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey, size: 17),
                      SizedBox(width: 4),
                      Text(
                        order.status,
                        style: TextStyle(
                          fontSize: 13.3,
                          color: Colors.grey[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Spacer(),
                      if (order.startAt != null)
                        Row(
                          children: [
                            Icon(Icons.access_time,
                                size: 16, color: Colors.grey.shade400),
                            SizedBox(width: 2),
                            Text(
                              order.startAt!,
                              style: TextStyle(
                                fontSize: 12.5,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// لنفترض order هو OrderModel الحالي
// Widget buildOrderImages(OrderModel order) {
//   final images = order.galleryUrls;
//   if (order.galleryUrls == null || order.galleryUrls!.isEmpty) {
//     // يمكنك هنا إرجاع SizedBox() أو Widget يظهر "لا يوجد صور" أو تتركه فارغ.
//     return SizedBox(); // فارغ ولا يأخذ مساحة
//     // أو يمكنك:
//     // return Text("No images available", style: TextStyle(color: Colors.grey));
//   }
//   return SizedBox(
//     height: 80,
//     child: ListView.separated(
//       scrollDirection: Axis.horizontal,
//       itemCount: order.galleryUrls!.length,
//       separatorBuilder: (_, __) => SizedBox(width: 8),
//       itemBuilder: (context, imgIndex) {
//         final imgUrl = order.galleryUrls?[imgIndex];
//         return ClipRRect(
//           borderRadius: BorderRadius.circular(8),
//           child: Image.network(
//             imgUrl!,
//             width: 80,
//             height: 80,
//             fit: BoxFit.cover,
//             errorBuilder: (_, __, ___) => Icon(Icons.broken_image,
//                 color: Colors.grey), // لو حدث خطأ في التحميل
//           ),
//         );
//       },
//     ),
//   );
// }
