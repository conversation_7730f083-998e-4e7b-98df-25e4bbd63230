import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/features/home/<USER>';
import 'package:renvo_app/features/home/<USER>/story_card.dart';

class UserHomeWidget extends StatelessWidget {
  const UserHomeWidget({
    super.key,
    required this.controller,
  });

  final HomePageController controller;

  @override
  Widget build(BuildContext context) {
    return ListView(
      // mainAxisAlignment: MainAxisAlignment.start,
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20, top: 20),
          child: Text(
            "Curated Stories",
            style: TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: 20,
          ),
          child: Text(
            "Discover New horizons",
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ),
        Obx(
          () => SizedBox(
            height: 250,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: controller.stories.length,
              itemBuilder: (context, index) {
                final story = controller.stories[index];
                return StoryCard(story: story);
              },
            ),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        TextButton(
            onPressed: () {
              Get.toNamed(Pages.Join_Provider.value);
            },
            child: Text("Join as a Services Providers"))
      ],
    );
  }
}
