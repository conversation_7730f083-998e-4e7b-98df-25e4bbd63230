import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:renvo_app/core/widgets/svg_icon.dart';

import 'package:renvo_app/gen/assets.gen.dart';

import '../controller.dart';

class NavBar extends StatelessWidget {
  const NavBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainPageController>();
    // MainPageController controller = Get.find();
    return Obx(
      () => NavigationBar(
        onDestinationSelected: (page) => controller.currentPage.value = page,
        selectedIndex: controller.currentPage.value,
        destinations: [
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.mainIcon),
            label: "home",
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.order),
            label: "my order",
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.add),
            label: "add",
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.chats),
            label: "chat",
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.profileImg),
            label: "profile",
          ),
        ],
      ),
    );
  }
}
