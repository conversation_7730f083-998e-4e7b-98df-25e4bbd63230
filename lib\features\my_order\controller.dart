import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/models/my_offer.dart';

enum OrderStatus { waiting, processing, completed, cancelled }

// class MyOrderPageController extends GetxController with GetSingleTickerProviderStateMixin {
//   late TabController tabController;
//   // المتغير الذي يحمل الحالة المختارة من التاب
//   var selectedStatus = OrderStatus.pending.obs;

//   @override
//   void onInit() {
//     super.onInit();
//     tabController = TabController(length: 4, vsync: this);

//     // كلما تغير التاب، نغير قيمة selectedStatus
//     tabController.addListener(() {
//       selectedStatus.value = OrderStatus.values[tabController.index];
//       // هنا ممكن ترسل api مع selectedStatus
//       fetchOrdersForStatus(selectedStatus.value);
//     });
//   }

//   void fetchOrdersForStatus(OrderStatus status) {
//     // هنا أرسل الطلب للباك باستخدام status
//     // print(status);
//     // مثال:
//     // APIService.getOrdersByStatus(status.name);
//   }

//   @override
//   void onClose() {
//     tabController.dispose();
//     super.onClose();
//   }
// }

class MyOrderPageController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  var selectedStatus = OrderStatus.waiting.obs;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);

    tabController.addListener(() {
      selectedStatus.value = OrderStatus.values[tabController.index];
    });
  }

  Future<void> ViewOffer(int id) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.view_c_offer(id),
        // method: RequestMethod.Delete,
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        fromJson: ViewOfferModel.fromJson,
      ),
    );

    @override
    void onClose() {
      tabController.dispose();
      super.onClose();
    }
  }
}
