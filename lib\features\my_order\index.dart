import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/my_order/controller.dart';

class MyOrderPage extends StatelessWidget {
  MyOrderPage({super.key});
  final MyOrderPageController controller = Get.put(MyOrderPageController());

  Widget build(BuildContext context) {
    final controller = Get.put(MyOrderPageController());

    return Scaffold(
      appBar: AppBar(
        title: const Text("My Order"),
        bottom: TabBar(
          controller: controller.tabController,
          tabs: const [
            Tab(text: "Pending"),
            Tab(text: "Underway"),
            Tab(text: "Complete"),
            Tab(text: "Canceled"),
          ],
        ),
      ),
      body: Tab<PERSON>ar<PERSON>iew(
        controller: controller.tabController,
        children: const [
          OrderCard(
            id: "32UD457",
            dateTime: "21 NOV at 03:00 PM",
            serviceCategory: "Services Category",
            serviceSubcategory: "Services Subcategory",
            description:
                "Here we write the services title - Here we write the services title ....",
            location: "Örbyhus - ICA Supermarket",
            showRating: true, // أو false حسب التبويب
            rating: 4.0,
          ),
          OrderCard(
            id: "32UD457",
            dateTime: "21 NOV at 03:00 PM",
            serviceCategory: "Services Category",
            serviceSubcategory: "Services Subcategory",
            description:
                "Here we write the services title - Here we write the services title ....",
            location: "Örbyhus - ICA Supermarket",
            showRating: true, // أو false حسب التبويب
            rating: 4.0,
          ),
          OrderCard(
            id: "32UD457",
            dateTime: "21 NOV at 03:00 PM",
            serviceCategory: "Services Category",
            serviceSubcategory: "Services Subcategory",
            description:
                "Here we write the services title - Here we write the services title ....",
            location: "Örbyhus - ICA Supermarket",
            // showRating: false, // أو false حسب التبويب
            rating: 4.0,
          ),

          // CardsPage(title: "Explore Content"),
          // CardsPage(title: "Saved Content"),
          // CardsPage(title: "Profile Content"),
        ],
      ),
    );
  }
}

// class CardsPage extends StatelessWidget {
//   final String title;
//   const CardsPage({super.key, required this.title});

//   @override
//   Widget build(BuildContext context) {
//     return ListView.builder(
//       padding: const EdgeInsets.all(16),
//       itemCount: 5,
//       itemBuilder: (context, index) {
//         return Card(
//           margin: const EdgeInsets.symmetric(vertical: 8),
//           child: ListTile(
//             title: Text('$title - Card ${index + 1}'),
//             subtitle: const Text("Description goes here"),
//           ),
//         );
//       },
//     );
//   }
// }

class OrderCard extends StatelessWidget {
  final String id;
  final String dateTime;
  final String serviceCategory;
  final String serviceSubcategory;
  final String description;
  final String location;
  final bool showRating; // true إذا كنا في completed
  final double rating; // التقييم بين 0 و 5

  const OrderCard({
    super.key,
    required this.id,
    required this.dateTime,
    required this.serviceCategory,
    required this.serviceSubcategory,
    required this.description,
    required this.location,
    this.showRating = false,
    this.rating = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الجزء العلوي
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "ID  $id",
                style:
                    TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
              ),
              Text(
                dateTime,
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // عنوان الخدمة
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: Colors.deepPurple,
                child: Icon(Icons.store, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    serviceCategory,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  Text(
                    serviceSubcategory,
                    style: TextStyle(color: Colors.grey.shade600),
                  )
                ],
              )
            ],
          ),
          const SizedBox(height: 12),
          Divider(),
          const SizedBox(height: 4),
          // الوصف
          Text(
            description,
            style: TextStyle(fontSize: 14, color: Colors.black),
          ),
          const SizedBox(height: 8),
          Divider(),
          const SizedBox(height: 8),
          // الموقع
          Row(
            children: [
              Icon(Icons.location_on_outlined, color: Colors.grey, size: 18),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  location,
                  style: TextStyle(
                      fontWeight: FontWeight.w500, color: Colors.grey.shade700),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // إما Rating أو View
          Align(
            alignment: Alignment.centerLeft,
            child: showRating
                ? Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < rating
                            ? Icons.star
                            : Icons.star_border_outlined,
                        size: 20,
                        color: Colors.amber,
                      );
                    }),
                  )
                : TextButton(
                    onPressed: () {},
                    child: Text(
                      "View",
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
